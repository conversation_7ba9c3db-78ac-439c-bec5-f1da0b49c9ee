package com.heal.controlcenter.dao.mysql.entity;

import com.heal.controlcenter.beans.AgentBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * DAO for agent operations.
 */
@Slf4j
@Repository
public class AgentDao {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * Gets agent bean data by identifier.
     * @param agentIdentifier The agent identifier
     * @return AgentBean if found, null otherwise
     */
    public AgentBean getAgentBeanData(String agentIdentifier) {
        String sql = "SELECT id, name, identifier, status, account_id as accountId " +
                "FROM agent WHERE identifier = ? AND status = 1";

        @SqlQuery("select a.id,unique_token uniqueToken,a.name,a.agent_type_id agentTypeId," +
                "a.created_time createdTime,a.updated_time updatedTime,a.user_details_id userDetailsId," +
                "a.status, a.host_address hostAddress, a.mode, a.description, a.physical_agent_id physicalAgentId, " +
                "a.forensics_enabled forensicsEnabled, a.version, pa.identifier physicalAgentIdentifier " +
                " from agent a, physical_agent pa where unique_token = :agentUid and a.physical_agent_id=pa.id")
        AgentBean getAgentBeanData(@Bind("agentUid") String agentUid);

        try {
            List<AgentBean> results = jdbcTemplate.query(sql, (rs, rowNum) -> {
                AgentBean bean = new AgentBean();
                bean.setId(rs.getInt("id"));
                bean.setName(rs.getString("name"));
                bean.setIdentifier(rs.getString("identifier"));
                bean.setStatus(rs.getInt("status"));
                bean.setAccountId(rs.getInt("accountId"));
                return bean;
            }, agentIdentifier);

            return results.isEmpty() ? null : results.get(0);
        } catch (Exception e) {
            log.error("Error getting agent bean data: {}", e.getMessage(), e);
            return null;
        }
    }
}
